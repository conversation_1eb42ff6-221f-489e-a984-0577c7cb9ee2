## Comprehensive Implementation Plan: Kubernetes Gateway API with Cilium on Kube-Hetzner

This plan outlines the steps to establish a GitOps-driven Kubernetes environment on Hetzner Cloud, leveraging `kube-hetzner`, Cilium's Gateway API, ArgoCD, and Terraform.

**Relevant Documentation & Repositories:**

  * **Kubernetes Gateway API:** [https://gateway-api.sigs.k8s.io/](https://gateway-api.sigs.k8s.io/) [1, 2, 3]
  * **Cilium Documentation:** [https://docs.cilium.io/](https://docs.cilium.io/)
      * Gateway API Specifics: [https://docs.cilium.io/en/stable/network/servicemesh/gateway-api/gateway-api/](https://docs.cilium.io/en/stable/network/servicemesh/gateway-api/gateway-api/) [4, 5]
      * Helm Reference: [https://docs.cilium.io/en/stable/helm-reference/](https://docs.cilium.io/en/stable/helm-reference/) [6]
  * **kube-hetzner (Terraform Module):** [https://github.com/kube-hetzner/terraform-hcloud-kube-hetzner](https://github.com/kube-hetzner/terraform-hcloud-kube-hetzner) [3, 7, 8]
      * Module Inputs: [https://registry.terraform.io/modules/kube-hetzner/kube-hetzner/hcloud/latest/docs?tab=inputs](https://www.google.com/search?q=https://registry.terraform.io/modules/kube-hetzner/kube-hetzner/hcloud/latest/docs%3Ftab%3Dinputs) [9, 10, 11]
  * **Terraform:** [https://www.terraform.io/docs](https://www.terraform.io/docs)
  * **ArgoCD Documentation:** [https://argo-cd.readthedocs.io/](https://argo-cd.readthedocs.io/)
  * **Cert-Manager Documentation:** [https://cert-manager.io/docs/](https://cert-manager.io/docs/)
  * **Hetzner Cloud Controller Manager (for LB Annotations):** [https://github.com/hetznercloud/hcloud-cloud-controller-manager](https://github.com/hetznercloud/hcloud-cloud-controller-manager) (Check for annotation list)
  * **Hetzner Cloud Tutorials (LB Annotations Example):** [https://community.hetzner.com/tutorials/kubernetes-on-hetzner-with-crio-flannel-and-hetzner-balancer/](https://community.hetzner.com/tutorials/kubernetes-on-hetzner-with-crio-flannel-and-hetzner-balancer/) [12]

-----

### Phase 0: Prerequisites & Foundational Setup

**Objective:** Prepare the development environment, accounts, and foundational GitOps repository.

**Steps:**

1.  **Install Required Tools:**
      * Terraform (latest stable version)
      * `kubectl` (compatible with your target Kubernetes versions)
      * Helm (v3+)
      * Git
      * `kubeseal` CLI (for Sealed Secrets, later phase)
2.  **Account Setup:**
      * Hetzner Cloud account: Obtain an API token.
      * GitHub account/organization: Prepare a monorepo for all configurations.
      * Terraform Cloud: Set up an account and configure workspaces for each cluster's Terraform state.
3.  **Git Repository Setup:**
      * Initialize the Git monorepo with a directory structure (e.g., `terraform/`, `argocd/`, `docs/`).
4.  **Understand Core Concepts:**
      * Familiarize with Kubernetes Gateway API (GatewayClass, Gateway, HTTPRoute, ReferenceGrant).[1, 2, 13]
      * Understand Cilium's role as a CNI and Gateway Controller.[4, 5]
      * Review `kube-hetzner` module features and inputs.[3, 9, 11]

**Verification:**

  * All tools are installed and accessible.
  * Hetzner API token is securely stored.
  * GitHub repository is created and accessible.
  * Terraform Cloud workspaces are configured.

-----

### Phase 1: Core Infrastructure Provisioning with Terraform & `kube-hetzner`

**Objective:** Provision the `main` (management) and `tenant` Kubernetes clusters using the `kube-hetzner` Terraform module, with Cilium as the CNI and Gateway API enabled.

**Steps:**

1.  **Terraform Configuration (`kube-hetzner` module):**
      * In your Terraform files (e.g., `terraform/main-cluster/main.tf`, `terraform/tenant-cluster-alpha/main.tf`):
          * Provide required variables: `hcloud_token`, `ssh_keys`, `cluster_name`, etc.
          * Set `ingress_controller = "none"` to disable default Ingress controllers.
          * Set `cni_plugin = "cilium"` to use Cilium as the CNI.[3]
          * Set `enable_cert_manager = true`.[11, 8]
          * Configure `cilium_values` (refer to `kube-hetzner` outputs [9] and Cilium Helm chart [6]):
              * Enable Cilium's Gateway API controller: `gatewayAPI.enabled: true`.[4, 5]
              * Ensure `kubeProxyReplacement` is set appropriately (e.g., `strict` or `partial`). `kube-hetzner` might configure this optimally when `cni_plugin = "cilium"` is chosen, but verify.[4, 5]
              * Enable Hubble for observability: `hubble.enabled: true`, `hubble.relay.enabled: true`, `hubble.ui.enabled: true`.[11, 14]
          * Configure `cert_manager_values` (refer to `kube-hetzner` outputs [9] and cert-manager Helm chart [15]):
              * Ensure cert-manager's Gateway API integration is enabled (e.g., `config.enableGatewayAPI: true` or similar, depending on the cert-manager chart version used by `kube-hetzner`).[8, 15]
              * Set `crds.enabled: true` if `kube-hetzner` doesn't manage cert-manager CRDs separately.[8]
          * Define server types for control planes and worker node pools as per your requirements.
2.  **Terraform Apply:**
      * Initialize Terraform: `terraform init`
      * Plan and apply for each cluster: `terraform plan` and `terraform apply`.
      * Store `kubeconfig` files securely.

**Verification:**

  * Terraform apply completes successfully for all clusters.
  * Clusters are accessible via `kubectl` using the generated `kubeconfig`.
  * Nodes (control-plane and workers) are in `Ready` state.
  * No Ingress controller pods (e.g., Traefik, Nginx Ingress) are running by default.
  * Cilium agent pods are running in the `kube-system` namespace on all nodes.
  * Cilium operator pods are running in the `kube-system` namespace.
  * Cert-manager pods are running (typically in `cert-manager` namespace).
  * Hubble pods (relay, UI) are running if enabled.

-----

### Phase 2: Gateway API CRDs and Cilium GatewayClass Setup

**Objective:** Install Kubernetes Gateway API Custom Resource Definitions (CRDs) and verify Cilium's `GatewayClass` is provisioned and accepted.

**Steps:**

1.  **Install Gateway API CRDs:**
      * **Option A (Recommended for GitOps - Terraform Managed):**
          * Use the `kubernetes_manifest` resource in Terraform to deploy the Gateway API CRDs from the official YAML manifest (`standard-install.yaml`). Ensure this runs after cluster creation but before any resources depending on these CRDs are created. You might need a separate Terraform apply step or use `depends_on`.
          * Reference: [https://gateway-api.sigs.k8s.io/guides/\#installing-gateway-api](https://www.google.com/search?q=https://gateway-api.sigs.k8s.io/guides/%23installing-gateway-api) [3, 7] (for the YAML URL) and [16] (for `kubernetes_manifest`).
      * **Option B (Manual/Scripted - Simpler Initial Step):**
          * On each cluster, apply the Standard Channel CRDs:
            `kubectl apply -f https://github.com/kubernetes-sigs/gateway-api/releases/latest/download/standard-install.yaml` [1, 3, 7]
2.  **Verify Cilium `GatewayClass`:**
      * Once Cilium is running with `gatewayAPI.enabled=true`, it should automatically create a `GatewayClass` resource.

**Verification:**

  * Gateway API CRDs are present in each cluster: `kubectl get crds | grep 'gateway.networking.k8s.io'`
  * A `GatewayClass` named `cilium` (or similar, confirm with Cilium documentation for the version deployed) exists: `kubectl get gatewayclass`.[4, 5]
  * The status of the `cilium` `GatewayClass` should show `Accepted: True`. `kubectl describe gatewayclass cilium`.

-----

### Phase 3: ArgoCD Setup on `main` Cluster

**Objective:** Deploy and configure ArgoCD on the `main` cluster for GitOps management of applications and platform tools.

**Steps:**

1.  **Deploy ArgoCD:**
      * Use Terraform's `helm_release` resource to deploy the official ArgoCD Helm chart onto the `main` cluster.
      * Configure values for High Availability (HA), resource requests/limits, and any other specific ArgoCD settings as per your "Overall Vision" document.
2.  **Configure ArgoCD:**
      * Set up SSO integration (e.g., Google Workspace, GitHub).
      * Configure notifications (e.g., Mattermost).
3.  **Expose ArgoCD (First use of Gateway API):**
      * Define a `Gateway` resource (managed by Terraform) in the `argocd` namespace on the `main` cluster.
          * `gatewayClassName: cilium`
          * Configure an HTTPS listener (port 443, `mode: Terminate`).
          * Add the annotation `cert-manager.io/cluster-issuer: <your-cluster-issuer-name>` (ClusterIssuer will be created in Phase 4).
          * Specify `certificateRefs` pointing to a Kubernetes `Secret` (e.g., `argocd-tls-secret`) that cert-manager will create and manage.
          * Add Hetzner Load Balancer annotations via `spec.infrastructure.annotations` on the `Gateway` resource (assuming Cilium 1.15+).[17, 18, 12]
      * Define an `HTTPRoute` resource (managed by Terraform or bootstrapped via ArgoCD itself) to route traffic from this `Gateway` to the `argo-cd-server` service.
4.  **Bootstrap ArgoCD:**
      * Use Terraform `kubernetes_manifest` resources to create initial ArgoCD `AppProject` CRDs and `Application` CRDs (e.g., "App of Apps" pattern, platform tools).

**Verification:**

  * ArgoCD pods (server, repo-server, application-controller, redis) are running and healthy in the `argocd` namespace.
  * ArgoCD UI is accessible via the configured hostname over HTTPS (once cert-manager and issuers are set up in Phase 4).
  * SSO and notifications are functional.
  * Initial `AppProject`s and `Application`s are created and synced in ArgoCD.
  * The `Gateway` for ArgoCD has an external IP address from Hetzner Load Balancer.
  * The `HTTPRoute` for ArgoCD is `Accepted` and `Programmed`.

-----

### Phase 4: Deploying Core Platform Applications with ArgoCD & Gateway API

**Objective:** Deploy cert-manager (configure Issuers), Prometheus, and Grafana using ArgoCD. Expose necessary UIs via Cilium Gateway API and secure them with TLS.

**Steps:**

1.  **Cert-Manager Configuration (Issuers):**
      * Define `ClusterIssuer` resources (e.g., `letsencrypt-staging` and `letsencrypt-prod`) using Terraform `kubernetes_manifest` (managed by ArgoCD or directly by Terraform).
      * Configure the `http01` solver to use Gateway API by specifying `gatewayHTTPRoute` settings in the solver configuration, referencing the `Gateway` that will handle challenges (this might be a dedicated Gateway or a shared one).[15]
2.  **Prometheus & Grafana Deployment:**
      * Define ArgoCD `Application` resources to deploy Prometheus and Grafana using their official Helm charts to the `main` cluster (or all clusters as needed, using ApplicationSets).
3.  **Expose Platform UIs (Grafana, Prometheus, Hubble UI if enabled):**
      * For each service UI to be exposed:
          * Ensure a `Gateway` resource exists or create a new one (managed by Terraform/ArgoCD) similar to the ArgoCD Gateway. This could be a shared Gateway for platform tools or individual ones.
              * `gatewayClassName: cilium`
              * HTTPS listener, `mode: Terminate`, `cert-manager.io/cluster-issuer` annotation, `certificateRefs`.
              * Hetzner Load Balancer annotations via `spec.infrastructure.annotations`.[17, 12]
          * Create `HTTPRoute` resources (managed by ArgoCD) in the respective namespaces, parenting to the appropriate `Gateway`.
              * Define rules for hostnames and paths to route to the Grafana, Prometheus, and Hubble UI services.
4.  **Verify TLS and Hetzner Load Balancer Configuration:**
      * Check that cert-manager successfully issues certificates for all exposed hostnames and stores them in the referenced `Secrets`.
      * Inspect the Hetzner Cloud Console to verify that Load Balancers are created with the specified names, types, locations, and are using private IPs for targets.

**Verification:**

  * `ClusterIssuer` resources are `Ready`.
  * Prometheus and Grafana pods are running and healthy.
  * `Gateway` resources for platform tools have external IP addresses.
  * `HTTPRoute`s for platform tools are `Accepted` and `Programmed`.
  * TLS certificates are successfully issued and applied.
  * Grafana, Prometheus UI, and Hubble UI (if exposed) are accessible via HTTPS with valid certificates.
  * Hetzner Load Balancers are configured as expected.

-----

### Phase 5: Client Application Deployment & Routing

**Objective:** Onboard and manage client applications, exposing them securely via Cilium Gateway API on tenant clusters.

**Steps:**

1.  **ArgoCD ApplicationSet for Client Apps:**
      * Implement the ArgoCD `ApplicationSet` strategy using the SCM Provider (GitHub) generator as outlined in your "Overall Vision" document.
      * Ensure generated `Application` CRs are assigned to appropriate non-privileged `AppProject`s.
2.  **Client Application Exposure:**
      * For each client application deployed on a tenant cluster:
          * ArgoCD will manage an `HTTPRoute` resource in the application's namespace.
          * This `HTTPRoute` will typically `parentRef` to a shared `Gateway` on the tenant cluster (e.g., `tenant-shared-gateway`). This shared `Gateway` would be provisioned by Terraform, similar to the management cluster's Gateways, with appropriate TLS and Hetzner LB configurations.
          * If an application `HTTPRoute` needs to reference a `Service` in a different namespace (and the `Gateway` allows routes from the app's namespace), ensure a `ReferenceGrant` is created in the `Service`'s namespace to permit this.[1, 19]
3.  **Advanced Routing (as needed):**
      * Utilize `HTTPRoute` filters for features like traffic splitting (canary/blue-green), request/response header modification, or URL rewrites.[13, 20, 21, 22, 23]
4.  **TLS for Client Applications:**
      * Handled by cert-manager annotating the shared `Gateway` listener(s) on tenant clusters. If applications require unique certificates for distinct hostnames not covered by a wildcard on the Gateway, individual `Certificate` resources or more granular Gateway listener configurations might be needed.

**Verification:**

  * Client applications are successfully deployed by ArgoCD to tenant clusters.
  * `HTTPRoute`s for client applications are `Accepted`, `Programmed`, and correctly route traffic.
  * `ReferenceGrant`s function as expected if used.
  * Advanced routing rules (traffic splitting, header mods) work as intended.
  * Client applications are accessible via HTTPS with valid TLS certificates.

-----

### Phase 6: Security Implementation (Network Policies, Secrets)

**Objective:** Enhance cluster security by implementing network policies and a secure secrets management workflow.

**Steps:**

1.  **Network Policies:**
      * Use Terraform to deploy a default-deny `CiliumNetworkPolicy` (or standard `NetworkPolicy`) in all tenant-managed namespaces upon creation.
      * Application teams will define and deploy specific `CiliumNetworkPolicy` (or `NetworkPolicy`) resources via their Helm charts (managed by ArgoCD) to allow necessary ingress/egress traffic.
2.  **Secrets Management (Bitnami Sealed Secrets):**
      * Deploy the Sealed Secrets controller to each cluster (via Terraform `helm_release` or ArgoCD `Application`).
      * Securely back up the master sealing private key from each cluster.
      * Establish a workflow for developers:
          * Fetch the public key from the target cluster's Sealed Secrets controller.
          * Use the `kubeseal` CLI to encrypt Kubernetes `Secret` YAML files into `SealedSecret` YAML files.
          * Commit `SealedSecret` files to application Git repositories.
          * ArgoCD applies the `SealedSecret` resources, which are then decrypted by the controller in the cluster.

**Verification:**

  * Default-deny network policies are in effect.
  * Application-specific network policies correctly allow required traffic and block unintended traffic.
  * Sealed Secrets controller is running on all clusters.
  * The master sealing key is securely backed up.
  * Developers can successfully seal secrets, and these are correctly decrypted and available to applications in the cluster.

-----

### Phase 7: Monitoring, Logging, and Observability

**Objective:** Configure and verify monitoring, logging, and observability tools, focusing on Cilium and Gateway API components.

**Steps:**

1.  **Prometheus & Grafana:**
      * Ensure Prometheus is scraping metrics from:
          * Cilium agents and operator.
          * Envoy proxies managed by Cilium Gateway API.
          * Kubernetes API server, kubelet, etc.
          * Applications (if they expose Prometheus metrics).
      * Set up Grafana dashboards for Cilium, Gateway API (Envoy), Kubernetes, and application performance.
2.  **Alertmanager:**
      * Configure Alertmanager (deployed via ArgoCD) to route alerts from Prometheus to Mattermost.
3.  **Cilium Hubble:**
      * Verify Hubble (enabled in Phase 1 via `cilium_values`) is operational.
      * If desired, expose the Hubble UI via an `HTTPRoute` and `Gateway`, secured with TLS, similar to other platform tools.
4.  **Gateway API Resource Status:**
      * Regularly check the `status` field of `GatewayClass`, `Gateway`, and `HTTPRoute` resources for conditions like `Accepted`, `Programmed`, and `ResolvedRefs`. Use `kubectl describe <resource_type> <resource_name> -n <namespace>` for detailed status and events.[5]
5.  **Logging:**
      * Review logs from Cilium agent/operator pods and Gateway (Envoy) pods for troubleshooting.
      * Plan for centralized log aggregation (Loki/Promtail or EFK/OpenSearch) as a high-priority next step (as per your "Future Considerations").

**Verification:**

  * Key metrics are visible in Grafana.
  * Alerts are correctly routed to Mattermost.
  * Hubble UI is accessible (if exposed) and displays network flow data.
  * `Gateway`, `GatewayClass`, and `HTTPRoute` resources show healthy statuses.
  * Logs from relevant components are accessible for troubleshooting.

-----

### Phase 8: Backup and Disaster Recovery

**Objective:** Implement and test backup and disaster recovery procedures for cluster state and persistent data.

**Steps:**

1.  **Kubernetes Cluster State (etcd):**
      * Configure and verify automated etcd backups using `kube-hetzner`'s k3s S3 snapshotting feature. Ensure the S3 target (e.g., Hetzner Storage Box) is correctly configured.
      * Document the k3s server token backup and cluster restoration procedure.
2.  **ArgoCD State:**
      * Primary DR is re-deploying ArgoCD configuration from Terraform and re-syncing applications from Git.
3.  **Persistent Volumes (PVs):**
      * Deploy Velero (via ArgoCD `Application`).
      * Configure Velero to back up PVs and specified Kubernetes resources to Hetzner Object Storage (or another S3-compatible service). Ensure correct S3 endpoint, credentials, and `s3ForcePathStyle: true` if needed.
      * Schedule regular backups.

**Verification:**

  * etcd backups are successfully created and stored in the S3 target.
  * A test restoration of etcd (in a non-production environment or as a dry run) is successful.
  * Velero is deployed and configured.
  * Test backups and restorations of sample applications with PVs are successful.
