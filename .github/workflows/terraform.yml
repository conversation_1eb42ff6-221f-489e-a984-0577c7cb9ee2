name: Terraform Apply

on:
  push:
    branches:
      - main
    paths:
      - 'terraform/**'
  workflow_dispatch:
    inputs:
      cluster:
        description: 'Cluster to apply (leave empty for all)'
        required: false
        type: string
      environment:
        description: 'Environment to use'
        required: false
        type: choice
        options:
          - dev
          - prod
        default: 'dev'

permissions:
  contents: read

jobs:
  terraform:
    name: Terraform
    runs-on: ubuntu-latest
    strategy:
      matrix:
        cluster: ${{ github.event.inputs.cluster == '' && fromJSON('["main"]') || fromJSON(format('["{}"]', github.event.inputs.cluster)) }}

    env:
      TF_VAR_hcloud_token: ${{ secrets.HCLOUD_TOKEN }}
      TF_VAR_ssh_public_key: ${{ secrets.SSH_PUBLIC_KEY }}
      TF_VAR_ssh_private_key: ${{ secrets.SSH_PRIVATE_KEY }}
      TF_VAR_hcloud_ssh_key_id: ${{ secrets.HCLOUD_SSH_KEY_ID }}
      TF_VAR_ssh_additional_public_keys: ${{ secrets.SSH_ADDITIONAL_PUBLIC_KEYS }}
      # TF_VAR_firewall_ssh_source_ip: ${{ secrets.FIREWALL_SSH_SOURCE_IP }}
      TF_TOKEN_app_terraform_io: ${{ secrets.TF_API_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Format Check
        id: fmt
        run: terraform fmt -check -recursive
        working-directory: terraform/${{ matrix.cluster }}
        continue-on-error: true

      - name: Terraform Init
        id: init
        run: terraform init --upgrade
        working-directory: terraform/${{ matrix.cluster }}

      - name: Terraform Validate
        id: validate
        run: terraform validate
        working-directory: terraform/${{ matrix.cluster }}

      - name: Terraform Plan
        id: plan
        run: terraform plan -no-color
        working-directory: terraform/${{ matrix.cluster }}
        continue-on-error: true

      - name: Terraform Plan Status
        if: steps.plan.outcome == 'failure'
        run: exit 1

      - name: Terraform Apply
        id: apply
        run: terraform apply -auto-approve
        working-directory: terraform/${{ matrix.cluster }}
